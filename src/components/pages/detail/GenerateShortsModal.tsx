import { useMemo, useState, useRef, useEffect } from 'react'
import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { screenOrientationAtom } from '@/stores'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { useAtomValue } from 'jotai'
import { MyMirrorAiTask } from '@/stores/types'
import { ShortsStatusEnum } from '@/apis/types'
import { publicPreLoadSourceObj } from '../../../configs/source';
import { VideoPlayer, VideoPlayerRef } from '../result/VideoPlayer'

export const GenerateShortsModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  shorts: {
    id?: string
    origin_url?: string
    url?: string
    status?: ShortsStatusEnum
    job_id?: string
    image_start_url?: string
  } | null
}> = ({ open, setOpen, shorts }) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const videoPlayerRef = useRef<VideoPlayerRef>(null)

  const handleClose = () => {
    setOpen?.(false)
  }

  const isPending = useMemo(() => {
    return shorts?.status === ShortsStatusEnum.PENDING_QUEUE || (shorts?.job_id && !shorts?.status)
  }, [shorts])

  return (
    <MyModal
      title=""
      open={open}
      width={screenOrientation.isLandScape ? 800 : '80vw'}
      content={<div className='min-h-[60dvh]'>
        {isPending && (
          <div className='py-32 text-center'>
            <img src={publicPreLoadSourceObj.painting} alt="" />
            <p className='text-[2rem] text-white/60'>{t('生成中')}</p>
          </div>
        )}
        {shorts?.status === ShortsStatusEnum.SUCCESS && (
          <div>
            <VideoPlayer videoUrl={shorts.origin_url! || shorts.url!} poster={shorts.image_start_url!} ref={videoPlayerRef} />
          </div>
        )}
        {shorts?.status === ShortsStatusEnum.FAILED && (
          <div className="py-32 text-center">
            <p className='text-[2rem] text-white/60'>{t('生成失败')}</p>
          </div>
        )}
      </div>}
      showOkButton={false}
      contentClassName="p-0 w-full"
      onCancel={handleClose}
    />
  )
}
